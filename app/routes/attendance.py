"""
User-facing attendance routes accessible to all authenticated users.
Allows employees to manage their own attendance records.
"""

from flask import Blueprint, render_template, request, redirect, url_for, current_app, jsonify
from flask_login import login_required, current_user
from datetime import date, datetime, timedelta

from app import db
from app.models.attendance import AttendanceRecord, AttendanceType
from app.models.employee import EmployeeDetail
from app.forms.attendance_record import AttendanceRecordForm, AttendanceRecordFilterForm
from app.services.attendance_service import AttendanceService
from app.utils.ajax_helpers import ajax_response
from app.utils.pagination import paginate_query
from app.utils.decorators import log_activity
from app.models import Activity

# Create the attendance Blueprint
attendance_bp = Blueprint('attendance', __name__, url_prefix='/attendance')


@attendance_bp.route('/', methods=['GET'])
@login_required
@log_activity('Viewed attendance records', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ)
def my_attendance():
    """Display user's own attendance records with filtering."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return render_template('attendance/no_employee_detail.html',
                             title="Attendance Records",
                             active_page="my_attendance")

    filter_form = AttendanceRecordFilterForm()

    # Build query for current user's records only
    query = AttendanceRecord.query.filter(
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).join(AttendanceType)

    # Apply filters if form is submitted
    if request.args:
        if request.args.get('attendance_type'):
            query = query.filter(AttendanceRecord.attendance_type_id == request.args.get('attendance_type'))

        if request.args.get('status'):
            query = query.filter(AttendanceRecord.status == request.args.get('status'))

        if request.args.get('start_date'):
            try:
                start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d').date()
                query = query.filter(AttendanceRecord.date >= start_date)
            except ValueError:
                pass

        if request.args.get('end_date'):
            try:
                end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d').date()
                query = query.filter(AttendanceRecord.date <= end_date)
            except ValueError:
                pass

    # Order by date descending
    query = query.order_by(AttendanceRecord.date.desc(), AttendanceRecord.created_at.desc())

    # Paginate
    per_page = current_app.config.get('PAGINATION_PER_PAGE', 15)
    attendance_records, pagination = paginate_query(query, per_page=per_page)

    return render_template('attendance/my_records.html',
                           attendance_records=attendance_records,
                           filter_form=filter_form,
                           title="My Attendance Records",
                           pagination=pagination,
                           active_page="my_attendance")


@attendance_bp.route('/request', methods=['GET'])
@login_required
@log_activity('Viewed attendance request form', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ)
def request_attendance():
    """Display form to request new attendance record."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return render_template('attendance/no_employee_detail.html',
                             title="Request Attendance",
                             active_page="request_attendance")

    form = AttendanceRecordForm()

    # Pre-populate employee field for current user
    form.employee_detail_id.data = current_user.employee_detail.id
    form.employee_detail_id.choices = [
        (current_user.employee_detail.id,
         f"{current_user.employee_detail.first_name} {current_user.employee_detail.last_name}")
    ]

    # Set default status to pending for user requests
    form.status.data = AttendanceRecord.STATUS_PENDING

    return render_template('attendance/request_form.html',
                           form=form,
                           title="Request Attendance",
                           active_page="request_attendance")


@attendance_bp.route('/request', methods=['POST'])
@login_required
@ajax_response(success_redirect='attendance.my_attendance')
@log_activity('Submitted attendance request', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def submit_attendance_request_form():
    """Handle submission of new attendance request."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return {
            'success': False,
            'message': 'Employee details not found. Please contact administrator.'
        }

    form = AttendanceRecordForm()

    # Ensure user can only submit for themselves
    form.employee_detail_id.data = current_user.employee_detail.id
    form.employee_detail_id.choices = [
        (current_user.employee_detail.id,
         f"{current_user.employee_detail.first_name} {current_user.employee_detail.last_name}")
    ]

    if form.validate_on_submit():
        # Use the service to create the record
        result = AttendanceService.create_attendance_record(
            employee_detail_id=current_user.employee_detail.id,  # Force current user
            attendance_type_id=form.attendance_type_id.data,
            record_date=form.date.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            duration_hours=form.duration_hours.data,
            notes=form.notes.data,
            status=AttendanceRecord.STATUS_PENDING,  # Force pending status for user requests
            holiday_work_reason=form.holiday_work_reason.data if form.is_holiday_work.data else None
        )

        if result['success']:
            message = "Attendance request submitted successfully!"

            # Add holiday warning to message if applicable
            if result.get('holiday_info', {}).get('is_holiday'):
                holiday_name = result['holiday_info']['holiday_name']
                message += f" Note: This request is for a holiday ({holiday_name}) and may require special approval."

            return {
                'success': True,
                'message': message,
                'redirect_url': url_for('attendance.my_attendance')
            }
        else:
            return {
                'success': False,
                'message': result['error'],
                'errors': form.errors
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@attendance_bp.route('/edit/<int:record_id>', methods=['GET'])
@login_required
@log_activity('Viewed attendance edit form', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ)
def edit_attendance(record_id):
    """Display form to edit existing attendance record."""
    # Get the record and ensure it belongs to current user
    record = AttendanceRecord.query.filter(
        AttendanceRecord.id == record_id,
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).first_or_404()

    # Check if record can be edited (only pending records)
    if record.status != AttendanceRecord.STATUS_PENDING:
        return render_template('attendance/cannot_edit.html',
                             record=record,
                             title="Cannot Edit Record",
                             active_page="my_attendance")

    form = AttendanceRecordForm(
        obj=record,
        original_date=record.date,
        original_employee_id=record.employee_detail_id,
        original_record_id=record.id
    )

    # Pre-populate employee field for current user
    form.employee_detail_id.data = current_user.employee_detail.id
    form.employee_detail_id.choices = [
        (current_user.employee_detail.id,
         f"{current_user.employee_detail.first_name} {current_user.employee_detail.last_name}")
    ]

    # Get holiday warning for current record
    holiday_warning = None
    if record.date and record.employee_detail_id:
        holiday_info = AttendanceService.check_holiday_date(record.employee_detail_id, record.date)
        if holiday_info.get('is_holiday'):
            holiday_warning = holiday_info

    return render_template('attendance/edit_form.html',
                           form=form,
                           record=record,
                           holiday_warning=holiday_warning,
                           title="Edit Attendance Request",
                           active_page="my_attendance")


@attendance_bp.route('/edit/<int:record_id>', methods=['POST'])
@login_required
@ajax_response(success_redirect='attendance.my_attendance')
@log_activity('Updated attendance request', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE)
def update_attendance(record_id):
    """Handle update of existing attendance record."""
    # Get the record and ensure it belongs to current user
    record = AttendanceRecord.query.filter(
        AttendanceRecord.id == record_id,
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).first_or_404()

    # Check if record can be edited (only pending records)
    if record.status != AttendanceRecord.STATUS_PENDING:
        return {
            'success': False,
            'message': 'Only pending attendance records can be edited.'
        }

    form = AttendanceRecordForm(
        obj=record,
        original_date=record.date,
        original_employee_id=record.employee_detail_id,
        original_record_id=record.id
    )

    # Ensure user can only update their own record
    form.employee_detail_id.data = current_user.employee_detail.id

    if form.validate_on_submit():
        # Use the service to update the record
        result = AttendanceService.update_attendance_record(
            record_id=record_id,
            employee_detail_id=current_user.employee_detail.id,  # Force current user
            attendance_type_id=form.attendance_type_id.data,
            date=form.date.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            duration_hours=form.duration_hours.data,
            notes=form.notes.data,
            status=AttendanceRecord.STATUS_PENDING,  # Keep as pending after edit
            is_holiday_work=form.is_holiday_work.data,
            holiday_work_reason=form.holiday_work_reason.data if form.is_holiday_work.data else None
        )

        if result['success']:
            return {
                'success': True,
                'message': 'Attendance request updated successfully!'
            }
        else:
            return {
                'success': False,
                'message': result['error'],
                'errors': form.errors
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@attendance_bp.route('/delete/<int:record_id>', methods=['POST'])
@login_required
@ajax_response(success_redirect='attendance.my_attendance')
@log_activity('Deleted attendance request', category=Activity.CATEGORY_DATA, method=Activity.METHOD_DELETE)
def delete_attendance(record_id):
    """Handle deletion of attendance record."""
    # Get the record and ensure it belongs to current user
    record = AttendanceRecord.query.filter(
        AttendanceRecord.id == record_id,
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).first_or_404()

    # Check if record can be deleted (only pending records)
    if record.status != AttendanceRecord.STATUS_PENDING:
        return {
            'success': False,
            'message': 'Only pending attendance records can be deleted.'
        }

    record_date = record.date.strftime('%Y-%m-%d')

    result = AttendanceService.delete_attendance_record(record_id)

    if result['success']:
        return {
            'success': True,
            'message': f'Attendance request for {record_date} deleted successfully!'
        }
    else:
        return {
            'success': False,
            'message': result['error']
        }


@attendance_bp.route('/form', methods=['GET'])
@login_required
def get_attendance_form():
    """Get attendance request form for drawer."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return render_template('attendance/no_employee_detail.html',
                             title="Request Attendance",
                             active_page="request_attendance")

    form = AttendanceRecordForm()

    # Pre-populate employee field for current user
    form.employee_detail_id.data = current_user.employee_detail.id
    form.employee_detail_id.choices = [
        (current_user.employee_detail.id,
         f"{current_user.employee_detail.first_name} {current_user.employee_detail.last_name}")
    ]

    return render_template('attendance/request_drawer_form.html', form=form)


@attendance_bp.route('/edit-form/<int:record_id>', methods=['GET'])
@login_required
def get_attendance_edit_form(record_id):
    """Get attendance edit form for drawer."""
    # Get the record and ensure it belongs to current user
    record = AttendanceRecord.query.filter(
        AttendanceRecord.id == record_id,
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).first_or_404()

    # Check if record can be edited (only pending records)
    if record.status != AttendanceRecord.STATUS_PENDING:
        return jsonify({
            'success': False,
            'error': 'Only pending attendance records can be edited.'
        }), 400

    form = AttendanceRecordForm(obj=record)

    # Pre-populate employee field for current user
    form.employee_detail_id.data = current_user.employee_detail.id
    form.employee_detail_id.choices = [
        (current_user.employee_detail.id,
         f"{current_user.employee_detail.first_name} {current_user.employee_detail.last_name}")
    ]

    return render_template('attendance/request_drawer_form.html',
                         form=form,
                         record=record,
                         is_edit=True,
                         action_url=url_for('attendance.update_attendance_drawer', record_id=record_id))


@attendance_bp.route('/can-edit/<int:record_id>', methods=['GET'])
@login_required
def can_edit_attendance(record_id):
    """Check if attendance record can be edited."""
    # Get the record and ensure it belongs to current user
    record = AttendanceRecord.query.filter(
        AttendanceRecord.id == record_id,
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).first_or_404()

    can_edit = record.status == AttendanceRecord.STATUS_PENDING
    reason = None if can_edit else f"Only pending attendance records can be edited. Current status: {record.status}"

    return jsonify({
        'can_edit': can_edit,
        'status': record.status,
        'reason': reason
    })


@attendance_bp.route('/update-drawer/<int:record_id>', methods=['POST'])
@login_required
@ajax_response(success_redirect='attendance.my_attendance')
@log_activity('Updated attendance request via drawer', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE)
def update_attendance_drawer(record_id):
    """Handle update of existing attendance record via drawer."""
    # Get the record and ensure it belongs to current user
    record = AttendanceRecord.query.filter(
        AttendanceRecord.id == record_id,
        AttendanceRecord.employee_detail_id == current_user.employee_detail.id
    ).first_or_404()

    # Check if record can be edited (only pending records)
    if record.status != AttendanceRecord.STATUS_PENDING:
        return {
            'success': False,
            'message': 'Only pending attendance records can be edited.'
        }

    form = AttendanceRecordForm()

    # Pre-populate employee field for current user
    form.employee_detail_id.data = current_user.employee_detail.id
    form.employee_detail_id.choices = [
        (current_user.employee_detail.id,
         f"{current_user.employee_detail.first_name} {current_user.employee_detail.last_name}")
    ]

    if form.validate_on_submit():
        # Use the service to update the record
        result = AttendanceService.update_attendance_record(
            record_id=record_id,
            employee_detail_id=current_user.employee_detail.id,  # Force current user
            attendance_type_id=form.attendance_type_id.data,
            date=form.date.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            duration_hours=form.duration_hours.data,
            notes=form.notes.data,
            status=AttendanceRecord.STATUS_PENDING,  # Keep as pending after edit
            is_holiday_work=form.is_holiday_work.data,
            holiday_work_reason=form.holiday_work_reason.data if form.is_holiday_work.data else None
        )

        if result['success']:
            return {
                'success': True,
                'message': 'Attendance request updated successfully!'
            }
        else:
            return {
                'success': False,
                'message': result['error'],
                'errors': form.errors
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@attendance_bp.route('/submit-drawer', methods=['POST'])
@login_required
def submit_attendance_request_drawer():
    """Submit attendance request via AJAX from drawer."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return jsonify({
            'success': False,
            'message': 'Employee details not found. Please contact administrator.'
        }), 400

    form = AttendanceRecordForm()

    if form.validate_on_submit():
        try:
            # Use the attendance service to create the record
            result = AttendanceService.create_attendance_record(
                employee_detail_id=current_user.employee_detail.id,
                attendance_type_id=form.attendance_type_id.data,
                record_date=form.date.data,
                start_time=form.start_time.data,
                end_time=form.end_time.data,
                duration_hours=form.duration_hours.data,
                notes=form.notes.data,
                status=AttendanceRecord.STATUS_PENDING,
                holiday_work_reason=form.holiday_work_reason.data if form.is_holiday_work.data else None
            )

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': 'Attendance request submitted successfully!',
                    'redirect_url': url_for('attendance.my_attendance')
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result['error']
                }), 400

        except Exception as e:
            current_app.logger.error(f"Error creating attendance request: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'An error occurred while submitting your request. Please try again.'
            }), 500

    # Return validation errors
    errors = {}
    for field, field_errors in form.errors.items():
        errors[field] = field_errors

    return jsonify({
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': errors
    }), 400


@attendance_bp.route('/calendar')
@login_required
@log_activity('Viewed attendance calendar', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ)
def attendance_calendar():
    """Display attendance calendar view."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return render_template('attendance/no_employee_detail.html',
                             title="Attendance Calendar",
                             active_page="attendance_calendar")

    # Get user's region for holiday display
    from app.services.holiday_service import HolidayService
    user_region = HolidayService.get_employee_region(current_user.employee_detail.id)

    return render_template('attendance/calendar.html',
                           title="Attendance Calendar",
                           active_page="attendance_calendar",
                           user_region=user_region)


# Manager/Admin routes (accessible to managers and admins for team management)
@attendance_bp.route('/team', methods=['GET'])
@login_required
@log_activity('Viewed team attendance', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ)
def team_attendance():
    """Display team attendance records (for managers and admins)."""
    # Only allow managers and admins
    if not (current_user.is_admin or current_user.is_manager):
        return redirect(url_for('attendance.my_attendance'))

    filter_form = AttendanceRecordFilterForm()

    # Build query based on user role
    if current_user.is_admin:
        # Admins can see all records
        query = AttendanceRecord.query.join(EmployeeDetail).join(AttendanceType)
    else:
        # Managers can see their team members' records
        # This will be enhanced when team relationships are fully implemented
        query = AttendanceRecord.query.join(EmployeeDetail).join(AttendanceType)

    # Apply filters similar to admin view
    if request.args:
        if request.args.get('employee'):
            query = query.filter(AttendanceRecord.employee_detail_id == request.args.get('employee'))

        if request.args.get('attendance_type'):
            query = query.filter(AttendanceRecord.attendance_type_id == request.args.get('attendance_type'))

        if request.args.get('status'):
            query = query.filter(AttendanceRecord.status == request.args.get('status'))

        if request.args.get('start_date'):
            try:
                start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d').date()
                query = query.filter(AttendanceRecord.date >= start_date)
            except ValueError:
                pass

        if request.args.get('end_date'):
            try:
                end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d').date()
                query = query.filter(AttendanceRecord.date <= end_date)
            except ValueError:
                pass

    # Order by date descending
    query = query.order_by(AttendanceRecord.date.desc(), AttendanceRecord.created_at.desc())

    # Paginate
    per_page = current_app.config.get('PAGINATION_PER_PAGE', 15)
    attendance_records, pagination = paginate_query(query, per_page=per_page)

    return render_template('attendance/team_records.html',
                           attendance_records=attendance_records,
                           filter_form=filter_form,
                           title="Team Attendance Records",
                           pagination=pagination,
                           active_page="team_attendance")
