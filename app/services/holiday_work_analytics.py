"""
Holiday Work Analytics Service

Provides analytics and reporting functionality for holiday work tracking.
Generates statistics, charts, and reports for management visibility.
"""

from datetime import date, datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy import func, and_, extract
from collections import defaultdict

from app import db
from app.models.attendance import AttendanceRecord
from app.models.employee import EmployeeDetail
from app.models.holiday import Holiday


class HolidayWorkAnalytics:
    """Service for holiday work analytics and reporting."""

    @staticmethod
    def get_overview_stats(start_date: Optional[date] = None, 
                          end_date: Optional[date] = None) -> Dict[str, Any]:
        """
        Get overview statistics for holiday work.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            Dictionary with overview statistics
        """
        # Build base query
        query = AttendanceRecord.query.filter(AttendanceRecord.is_holiday_work == True)
        
        if start_date:
            query = query.filter(AttendanceRecord.date >= start_date)
        if end_date:
            query = query.filter(AttendanceRecord.date <= end_date)
            
        # Get basic counts
        total_holiday_work = query.count()
        approved_holiday_work = query.filter(
            AttendanceRecord.status.in_([
                AttendanceRecord.STATUS_APPROVED, 
                AttendanceRecord.STATUS_AUTO_APPROVED
            ])
        ).count()
        pending_holiday_work = query.filter(
            AttendanceRecord.status == AttendanceRecord.STATUS_PENDING
        ).count()
        rejected_holiday_work = query.filter(
            AttendanceRecord.status == AttendanceRecord.STATUS_REJECTED
        ).count()
        
        # Get unique employees who worked on holidays
        unique_employees = query.with_entities(
            AttendanceRecord.employee_detail_id
        ).distinct().count()
        
        # Calculate approval rate
        approval_rate = 0
        if total_holiday_work > 0:
            approval_rate = round((approved_holiday_work / total_holiday_work) * 100, 1)
            
        return {
            'total_holiday_work': total_holiday_work,
            'approved_holiday_work': approved_holiday_work,
            'pending_holiday_work': pending_holiday_work,
            'rejected_holiday_work': rejected_holiday_work,
            'unique_employees': unique_employees,
            'approval_rate': approval_rate,
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }

    @staticmethod
    def get_monthly_trends(year: int) -> Dict[str, Any]:
        """
        Get monthly holiday work trends for a specific year.
        
        Args:
            year: Year to analyze
            
        Returns:
            Dictionary with monthly trend data
        """
        # Get holiday work records for the year
        records = AttendanceRecord.query.filter(
            and_(
                AttendanceRecord.is_holiday_work == True,
                extract('year', AttendanceRecord.date) == year
            )
        ).all()
        
        # Initialize monthly data
        monthly_data = {}
        for month in range(1, 13):
            monthly_data[month] = {
                'month': month,
                'month_name': datetime(year, month, 1).strftime('%B'),
                'total': 0,
                'approved': 0,
                'pending': 0,
                'rejected': 0,
                'unique_employees': set()
            }
        
        # Process records
        for record in records:
            month = record.date.month
            monthly_data[month]['total'] += 1
            monthly_data[month]['unique_employees'].add(record.employee_detail_id)
            
            if record.status in [AttendanceRecord.STATUS_APPROVED, AttendanceRecord.STATUS_AUTO_APPROVED]:
                monthly_data[month]['approved'] += 1
            elif record.status == AttendanceRecord.STATUS_PENDING:
                monthly_data[month]['pending'] += 1
            elif record.status == AttendanceRecord.STATUS_REJECTED:
                monthly_data[month]['rejected'] += 1
        
        # Convert sets to counts and format data
        trend_data = []
        for month_data in monthly_data.values():
            month_data['unique_employees'] = len(month_data['unique_employees'])
            trend_data.append(month_data)
            
        return {
            'year': year,
            'monthly_trends': trend_data,
            'total_year': sum(m['total'] for m in trend_data),
            'peak_month': max(trend_data, key=lambda x: x['total'])['month_name'] if any(m['total'] > 0 for m in trend_data) else None
        }

    @staticmethod
    def get_employee_rankings(start_date: Optional[date] = None,
                            end_date: Optional[date] = None,
                            limit: int = 10) -> Dict[str, Any]:
        """
        Get employee rankings by holiday work frequency.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Maximum number of employees to return
            
        Returns:
            Dictionary with employee ranking data
        """
        # Build query
        query = db.session.query(
            AttendanceRecord.employee_detail_id,
            EmployeeDetail.first_name,
            EmployeeDetail.last_name,
            EmployeeDetail.employee_number,
            func.count(AttendanceRecord.id).label('holiday_work_count'),
            func.count(
                func.case(
                    (AttendanceRecord.status.in_([
                        AttendanceRecord.STATUS_APPROVED,
                        AttendanceRecord.STATUS_AUTO_APPROVED
                    ]), 1)
                )
            ).label('approved_count')
        ).join(
            EmployeeDetail, AttendanceRecord.employee_detail_id == EmployeeDetail.id
        ).filter(
            AttendanceRecord.is_holiday_work == True
        )
        
        if start_date:
            query = query.filter(AttendanceRecord.date >= start_date)
        if end_date:
            query = query.filter(AttendanceRecord.date <= end_date)
            
        # Group by employee and order by count
        rankings = query.group_by(
            AttendanceRecord.employee_detail_id,
            EmployeeDetail.first_name,
            EmployeeDetail.last_name,
            EmployeeDetail.employee_number
        ).order_by(
            func.count(AttendanceRecord.id).desc()
        ).limit(limit).all()
        
        # Format results
        employee_data = []
        for rank, (emp_id, first_name, last_name, emp_number, total_count, approved_count) in enumerate(rankings, 1):
            approval_rate = round((approved_count / total_count) * 100, 1) if total_count > 0 else 0
            employee_data.append({
                'rank': rank,
                'employee_id': emp_id,
                'employee_name': f"{first_name} {last_name}",
                'employee_number': emp_number,
                'holiday_work_count': total_count,
                'approved_count': approved_count,
                'approval_rate': approval_rate
            })
            
        return {
            'rankings': employee_data,
            'total_employees': len(employee_data),
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }

    @staticmethod
    def get_holiday_breakdown(start_date: Optional[date] = None,
                            end_date: Optional[date] = None) -> Dict[str, Any]:
        """
        Get breakdown of holiday work by specific holidays.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            Dictionary with holiday breakdown data
        """
        # Get holiday work records with holiday information
        query = db.session.query(
            AttendanceRecord.date,
            func.count(AttendanceRecord.id).label('work_count'),
            func.count(
                func.case(
                    (AttendanceRecord.status.in_([
                        AttendanceRecord.STATUS_APPROVED,
                        AttendanceRecord.STATUS_AUTO_APPROVED
                    ]), 1)
                )
            ).label('approved_count')
        ).filter(
            AttendanceRecord.is_holiday_work == True
        )
        
        if start_date:
            query = query.filter(AttendanceRecord.date >= start_date)
        if end_date:
            query = query.filter(AttendanceRecord.date <= end_date)
            
        holiday_work_by_date = query.group_by(AttendanceRecord.date).all()
        
        # Get holiday information for these dates
        work_dates = [record.date for record in holiday_work_by_date]
        holidays = Holiday.query.filter(Holiday.date.in_(work_dates)).all()
        
        # Create holiday lookup
        holiday_lookup = {holiday.date: holiday for holiday in holidays}
        
        # Format results
        holiday_data = []
        for date_record in holiday_work_by_date:
            work_date = date_record.date
            holiday = holiday_lookup.get(work_date)
            
            holiday_data.append({
                'date': work_date.isoformat(),
                'holiday_name': holiday.name if holiday else 'Unknown Holiday',
                'region_code': holiday.region_code if holiday else 'UNKNOWN',
                'work_count': date_record.work_count,
                'approved_count': date_record.approved_count,
                'approval_rate': round((date_record.approved_count / date_record.work_count) * 100, 1) if date_record.work_count > 0 else 0
            })
        
        # Sort by work count descending
        holiday_data.sort(key=lambda x: x['work_count'], reverse=True)
        
        return {
            'holiday_breakdown': holiday_data,
            'total_holidays_worked': len(holiday_data),
            'total_work_instances': sum(h['work_count'] for h in holiday_data),
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }

    @staticmethod
    def get_dashboard_summary(days_back: int = 30) -> Dict[str, Any]:
        """
        Get summary data for dashboard widgets.
        
        Args:
            days_back: Number of days to look back for recent data
            
        Returns:
            Dictionary with dashboard summary data
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back)
        
        # Get recent overview
        recent_stats = HolidayWorkAnalytics.get_overview_stats(start_date, end_date)
        
        # Get year-to-date stats
        ytd_start = date(end_date.year, 1, 1)
        ytd_stats = HolidayWorkAnalytics.get_overview_stats(ytd_start, end_date)
        
        # Get top employees (recent)
        top_employees = HolidayWorkAnalytics.get_employee_rankings(start_date, end_date, 5)
        
        # Get upcoming holidays with potential work
        upcoming_holidays = Holiday.query.filter(
            and_(
                Holiday.date >= end_date,
                Holiday.date <= end_date + timedelta(days=30)
            )
        ).order_by(Holiday.date).limit(5).all()
        
        upcoming_data = []
        for holiday in upcoming_holidays:
            upcoming_data.append({
                'date': holiday.date.isoformat(),
                'name': holiday.name,
                'region_code': holiday.region_code,
                'days_until': (holiday.date - end_date).days
            })
        
        return {
            'recent_period': {
                'days': days_back,
                'stats': recent_stats
            },
            'year_to_date': ytd_stats,
            'top_employees_recent': top_employees['rankings'],
            'upcoming_holidays': upcoming_data,
            'generated_at': datetime.now().isoformat()
        }
