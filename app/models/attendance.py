"""
Attendance management models.
"""
from datetime import datetime, time
from decimal import Decimal

from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON> # Using JSONB for schedule_pattern for better performance if using PostgreSQL
from sqlalchemy import Text

from app import db
from app.models import PH_TZ # Assuming PH_TZ is defined in app.models.__init__ or similar

class Holiday(db.Model):
    __tablename__ = 'holidays'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    date = db.Column(db.Date, nullable=False)
    description = db.Column(db.Text, nullable=True)
    region_code = db.Column(db.String(10), nullable=False, default='GLOBAL') # e.g., "US", "PH", "GLOBAL"
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    # Add composite unique constraint to allow same date for different regions
    __table_args__ = (
        db.UniqueConstraint('date', 'region_code', name='uq_holiday_date_region'),
        db.Index('idx_holiday_date', 'date'),
        db.Index('idx_holiday_region', 'region_code'),
        db.Index('idx_holiday_date_region', 'date', 'region_code'),
    )

    def __repr__(self):
        return f'<Holiday {self.name} on {self.date} ({self.region_code})>'

    def to_dict(self):
        """Convert holiday to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'name': self.name,
            'date': self.date.isoformat() if self.date else None,
            'description': self.description,
            'region_code': self.region_code,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_by_region(cls, region_code, start_date=None, end_date=None):
        """Get holidays for a specific region within date range."""
        query = cls.query.filter(
            db.or_(cls.region_code == region_code, cls.region_code == 'GLOBAL')
        )

        if start_date:
            query = query.filter(cls.date >= start_date)
        if end_date:
            query = query.filter(cls.date <= end_date)

        return query.order_by(cls.date).all()

    @classmethod
    def get_by_date_range(cls, start_date, end_date, region_code=None):
        """Get holidays within a date range, optionally filtered by region."""
        query = cls.query.filter(
            cls.date >= start_date,
            cls.date <= end_date
        )

        if region_code:
            query = query.filter(
                db.or_(cls.region_code == region_code, cls.region_code == 'GLOBAL')
            )

        return query.order_by(cls.date).all()

    @classmethod
    def is_holiday(cls, date, region_code):
        """Check if a specific date is a holiday for the given region."""
        return cls.query.filter(
            cls.date == date,
            db.or_(cls.region_code == region_code, cls.region_code == 'GLOBAL')
        ).first() is not None

    @classmethod
    def get_upcoming_holidays(cls, region_code, days=30):
        """Get upcoming holidays for a region within the next N days."""
        from datetime import date, timedelta

        start_date = date.today()
        end_date = start_date + timedelta(days=days)

        return cls.get_by_region(region_code, start_date, end_date)

class WorkScheduleDefinition(db.Model):
    __tablename__ = 'work_schedule_definitions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    is_default_template = db.Column(db.Boolean, default=False, nullable=False)
    # Example: {"Mon": {"start_time": "09:00", "end_time": "18:00", "is_off_day": false}, ...}
    # This can serve as a template pattern if needed, or just be descriptive.
    # The actual daily schedule is in EmployeeSchedule.schedule_pattern
    template_pattern_info = db.Column(db.Text, nullable=True) # Store general info about the pattern
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    employee_schedules = db.relationship('EmployeeSchedule', back_populates='work_schedule_definition')

    def __repr__(self):
        return f'<WorkScheduleDefinition {self.name}>'

class EmployeeSchedule(db.Model):
    __tablename__ = 'employee_schedules'

    id = db.Column(db.Integer, primary_key=True)
    employee_detail_id = db.Column(db.Integer, db.ForeignKey('employee_details.id', name='fk_employee_schedule_employee_detail_id'), nullable=False)
    work_schedule_definition_id = db.Column(db.Integer, db.ForeignKey('work_schedule_definitions.id', name='fk_employee_schedule_work_schedule_definition_id'), nullable=True)

    effective_start_date = db.Column(db.Date, nullable=False)
    effective_end_date = db.Column(db.Date, nullable=False) # Inclusive end date for this schedule period

    # Stores the actual day-by-day work pattern for this specific period.
    # Example: {"Mon": {"start_time": "09:00", "end_time": "18:00", "is_off_day": false, "notes": "Standard"}, ...}
    # Use simple JSON type that works across all databases
    schedule_pattern = db.Column(
        db.JSON(),
        nullable=False
    )

    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    employee_detail = db.relationship('EmployeeDetail', backref=db.backref('employee_schedules', lazy='dynamic'))
    work_schedule_definition = db.relationship('WorkScheduleDefinition', back_populates='employee_schedules')

    __table_args__ = (
        db.Index('idx_employee_schedule_employee_date_range', 'employee_detail_id', 'effective_start_date', 'effective_end_date'),
    )

    def __repr__(self):
        return f'<EmployeeSchedule for {self.employee_detail.first_name if self.employee_detail else "N/A"} from {self.effective_start_date} to {self.effective_end_date}>'

class AttendanceType(db.Model):
    __tablename__ = 'attendance_types'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(10), nullable=False, unique=True) # e.g., "PVL", "SL", "RTO"
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    requires_approval = db.Column(db.Boolean, default=False, nullable=False)
    is_full_day = db.Column(db.Boolean, default=True, nullable=False) # If false, implies partial day
    color_code = db.Column(db.String(10), nullable=True) # e.g., "#FF0000" for UI
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    attendance_records = db.relationship('AttendanceRecord', back_populates='attendance_type')

    def __repr__(self):
        return f'<AttendanceType {self.name} ({self.code})>'

class AttendanceRecord(db.Model):
    __tablename__ = 'attendance_records'

    STATUS_PENDING = 'Pending'
    STATUS_APPROVED = 'Approved'
    STATUS_REJECTED = 'Rejected'
    STATUS_CANCELLED = 'Cancelled'
    STATUS_AUTO_APPROVED = 'Auto-Approved' # For types not requiring approval

    STATUS_CHOICES = [
        (STATUS_PENDING, 'Pending'),
        (STATUS_APPROVED, 'Approved'),
        (STATUS_REJECTED, 'Rejected'),
        (STATUS_CANCELLED, 'Cancelled'),
        (STATUS_AUTO_APPROVED, 'Auto-Approved'),
    ]

    # Holiday work compensation types
    COMPENSATION_OVERTIME_PAY = 'overtime_pay'
    COMPENSATION_COMP_TIME = 'comp_time'
    COMPENSATION_NONE = 'none'

    COMPENSATION_CHOICES = [
        (COMPENSATION_OVERTIME_PAY, 'Overtime Pay'),
        (COMPENSATION_COMP_TIME, 'Compensatory Time'),
        (COMPENSATION_NONE, 'No Compensation'),
    ]

    id = db.Column(db.Integer, primary_key=True)
    employee_detail_id = db.Column(db.Integer, db.ForeignKey('employee_details.id', name='fk_attendance_record_employee_detail_id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    attendance_type_id = db.Column(db.Integer, db.ForeignKey('attendance_types.id', name='fk_attendance_record_attendance_type_id'), nullable=False)

    start_time = db.Column(db.Time, nullable=True) # For partial day types like HD, OCW
    end_time = db.Column(db.Time, nullable=True)   # For partial day types like HD, OCW
    duration_hours = db.Column(db.Numeric(4, 2), nullable=True) # e.g., 4.00 for half-day

    notes = db.Column(db.Text, nullable=True) # Remarks by employee or approver
    status = db.Column(db.String(20), default=STATUS_PENDING, nullable=False)

    approved_by_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_attendance_record_approved_by_id'), nullable=True) # FK to User.id
    approved_at = db.Column(db.DateTime, nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)

    # Holiday work tracking fields
    is_holiday_work = db.Column(db.Boolean, default=False, nullable=False) # Flag for work performed on holidays
    holiday_work_reason = db.Column(db.Text, nullable=True) # Justification for working on holiday
    holiday_work_compensation_type = db.Column(db.String(20), default=COMPENSATION_NONE, nullable=True) # Type of compensation

    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    employee_detail = db.relationship('EmployeeDetail', backref=db.backref('attendance_records', lazy='dynamic'))
    attendance_type = db.relationship('AttendanceType', back_populates='attendance_records')
    approved_by = db.relationship('User', foreign_keys=[approved_by_id]) # Relationship to User who approved

    __table_args__ = (
        db.Index('idx_attendance_record_employee_date', 'employee_detail_id', 'date'),
        db.Index('idx_attendance_record_status', 'status'),
        db.Index('idx_attendance_record_holiday_work', 'is_holiday_work'),
        db.Index('idx_attendance_record_holiday_work_date', 'is_holiday_work', 'date'),
    )

    def get_status_display(self):
        """Get human-readable status display."""
        status_map = {
            self.STATUS_PENDING: 'Pending',
            self.STATUS_APPROVED: 'Approved',
            self.STATUS_REJECTED: 'Rejected',
            self.STATUS_CANCELLED: 'Cancelled',
            self.STATUS_AUTO_APPROVED: 'Auto-Approved'
        }
        return status_map.get(self.status, self.status)

    def get_compensation_display(self):
        """Get human-readable compensation type display."""
        compensation_map = {
            self.COMPENSATION_OVERTIME_PAY: 'Overtime Pay',
            self.COMPENSATION_COMP_TIME: 'Compensatory Time',
            self.COMPENSATION_NONE: 'No Compensation'
        }
        return compensation_map.get(self.holiday_work_compensation_type, 'No Compensation')

    def is_holiday_work_eligible(self):
        """Check if this record is eligible for holiday work compensation."""
        return (
            self.is_holiday_work and
            self.status in [self.STATUS_APPROVED, self.STATUS_AUTO_APPROVED] and
            self.holiday_work_compensation_type != self.COMPENSATION_NONE
        )

    def to_dict(self):
        """Convert attendance record to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'employee_detail_id': self.employee_detail_id,
            'employee_name': f"{self.employee_detail.first_name} {self.employee_detail.last_name}" if self.employee_detail else None,
            'date': self.date.isoformat() if self.date else None,
            'attendance_type_id': self.attendance_type_id,
            'attendance_type_code': self.attendance_type.code if self.attendance_type else None,
            'attendance_type_name': self.attendance_type.name if self.attendance_type else None,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_hours': float(self.duration_hours) if self.duration_hours else None,
            'notes': self.notes,
            'status': self.status,
            'status_display': self.get_status_display(),
            'is_holiday_work': self.is_holiday_work,
            'holiday_work_reason': self.holiday_work_reason,
            'holiday_work_compensation_type': self.holiday_work_compensation_type,
            'compensation_display': self.get_compensation_display(),
            'is_holiday_work_eligible': self.is_holiday_work_eligible(),
            'approved_by_id': self.approved_by_id,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'rejection_reason': self.rejection_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_holiday_work_records(cls, start_date=None, end_date=None, employee_id=None, status=None):
        """Get holiday work records with optional filtering."""
        query = cls.query.filter(cls.is_holiday_work == True)

        if start_date:
            query = query.filter(cls.date >= start_date)
        if end_date:
            query = query.filter(cls.date <= end_date)
        if employee_id:
            query = query.filter(cls.employee_detail_id == employee_id)
        if status:
            query = query.filter(cls.status == status)

        return query.order_by(cls.date.desc()).all()

    @classmethod
    def get_holiday_work_stats(cls, start_date=None, end_date=None):
        """Get holiday work statistics."""
        query = cls.query.filter(cls.is_holiday_work == True)

        if start_date:
            query = query.filter(cls.date >= start_date)
        if end_date:
            query = query.filter(cls.date <= end_date)

        total_records = query.count()
        approved_records = query.filter(cls.status.in_([cls.STATUS_APPROVED, cls.STATUS_AUTO_APPROVED])).count()
        pending_records = query.filter(cls.status == cls.STATUS_PENDING).count()

        compensation_stats = {}
        for comp_type, comp_name in cls.COMPENSATION_CHOICES:
            count = query.filter(cls.holiday_work_compensation_type == comp_type).count()
            compensation_stats[comp_type] = {'name': comp_name, 'count': count}

        return {
            'total_records': total_records,
            'approved_records': approved_records,
            'pending_records': pending_records,
            'compensation_stats': compensation_stats
        }

    def __repr__(self):
        holiday_indicator = " [HOLIDAY WORK]" if self.is_holiday_work else ""
        return f'<AttendanceRecord for {self.employee_detail.first_name if self.employee_detail else "N/A"} on {self.date} - {self.attendance_type.code if self.attendance_type else "N/A"}{holiday_indicator}>'
